"""
Video thumbnail generation utilities for FurEverMemories
"""
import os
import subprocess
import tempfile
from PIL import Image
from flask import current_app
import logging

logger = logging.getLogger(__name__)

def generate_video_thumbnail(video_path, output_path, timestamp="00:00:01", size=(150, 150)):
    """
    Generate a thumbnail from a video file using ffmpeg
    
    Args:
        video_path (str): Path to the video file
        output_path (str): Path where thumbnail should be saved
        timestamp (str): Timestamp to extract frame from (format: HH:MM:SS)
        size (tuple): Size of the thumbnail (width, height)
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if ffmpeg is available
        if not is_ffmpeg_available():
            logger.warning("ffmpeg not available, using fallback method")
            return generate_fallback_thumbnail(output_path, size)
        
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Generate thumbnail using ffmpeg
        cmd = [
            'ffmpeg',
            '-i', video_path,
            '-ss', timestamp,
            '-vframes', '1',
            '-vf', f'scale={size[0]}:{size[1]}:force_original_aspect_ratio=decrease,pad={size[0]}:{size[1]}:(ow-iw)/2:(oh-ih)/2',
            '-y',  # Overwrite output file
            output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            logger.info(f"Successfully generated thumbnail: {output_path}")
            return True
        else:
            logger.error(f"ffmpeg failed: {result.stderr}")
            return generate_fallback_thumbnail(output_path, size)
            
    except subprocess.TimeoutExpired:
        logger.error("ffmpeg timeout while generating thumbnail")
        return generate_fallback_thumbnail(output_path, size)
    except Exception as e:
        logger.error(f"Error generating video thumbnail: {str(e)}")
        return generate_fallback_thumbnail(output_path, size)

def generate_fallback_thumbnail(output_path, size=(150, 150)):
    """
    Generate a fallback thumbnail with a video icon when ffmpeg is not available

    Args:
        output_path (str): Path where thumbnail should be saved
        size (tuple): Size of the thumbnail (width, height)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        from PIL import ImageDraw

        # Create a thumbnail with a dark background
        img = Image.new('RGB', size, color='#343a40')
        draw = ImageDraw.Draw(img)

        # Calculate center
        center_x, center_y = size[0] // 2, size[1] // 2

        # Draw a play button icon
        triangle_size = min(size) // 5  # Scale with image size
        triangle_points = [
            (center_x - triangle_size//2, center_y - triangle_size//2),
            (center_x - triangle_size//2, center_y + triangle_size//2),
            (center_x + triangle_size//2, center_y)
        ]
        draw.polygon(triangle_points, fill='white')

        # Draw a circle around it
        circle_radius = min(size) // 4
        circle_bbox = [
            center_x - circle_radius, center_y - circle_radius,
            center_x + circle_radius, center_y + circle_radius
        ]
        draw.ellipse(circle_bbox, outline='white', width=2)

        # Save the image
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        img.save(output_path, 'JPEG', quality=85)

        logger.info(f"Generated fallback thumbnail: {output_path}")
        return True

    except Exception as e:
        logger.error(f"Error generating fallback thumbnail: {str(e)}")
        return False

def is_ffmpeg_available():
    """
    Check if ffmpeg is available on the system
    
    Returns:
        bool: True if ffmpeg is available, False otherwise
    """
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, timeout=5)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False

def get_video_thumbnail_path(user_id, filename):
    """
    Get the path where video thumbnail should be stored

    Args:
        user_id (int): User ID
        filename (str): Original filename

    Returns:
        str: Path to thumbnail file
    """
    # Remove extension and add _thumb.jpg
    base_name = os.path.splitext(filename)[0]
    thumbnail_filename = f"{base_name}_thumb.jpg"

    # Use the same directory structure as uploads
    thumbnail_path = os.path.join(
        current_app.root_path,
        'static',
        'uploads',
        str(user_id),
        thumbnail_filename
    )

    return thumbnail_path

def get_video_thumbnail_url(user_id, filename):
    """
    Get the URL for accessing video thumbnail

    Args:
        user_id (int): User ID
        filename (str): Original filename

    Returns:
        str: URL to thumbnail
    """
    from flask import url_for
    base_name = os.path.splitext(filename)[0]
    thumbnail_filename = f"{base_name}_thumb.jpg"

    return url_for('static', filename=f"uploads/{user_id}/{thumbnail_filename}")

def ensure_video_thumbnail(upload):
    """
    Ensure a video thumbnail exists for the given upload

    Args:
        upload: Upload model instance

    Returns:
        str: URL to thumbnail or None if failed
    """
    if upload.file_type != 'video':
        return None

    # Get paths
    video_path = get_file_path(upload.s3_key)
    thumbnail_path = get_video_thumbnail_path(upload.user_id, upload.file_name)

    # Check if thumbnail already exists
    if os.path.exists(thumbnail_path):
        return get_video_thumbnail_url(upload.user_id, upload.file_name)

    # Generate thumbnail if video file exists
    if os.path.exists(video_path):
        if generate_video_thumbnail(video_path, thumbnail_path):
            return get_video_thumbnail_url(upload.user_id, upload.file_name)

    return None

def get_file_path(s3_key):
    """
    Get local file path from S3 key

    Args:
        s3_key (str): S3 key

    Returns:
        str: Local file path
    """
    return os.path.join(current_app.root_path, 'static', s3_key)
