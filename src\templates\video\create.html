{% extends "base.html" %}

{% block title %}Create Your Memorial Video - FurEverMemories{% endblock %}

{% block styles %}
<style>
    .preview-container {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        background-color: #f8f9fa;
        border-radius: 5px;
        overflow: hidden;
    }

    .preview-placeholder {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #6c757d;
    }

    .preview-video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .customization-option {
        margin-bottom: 1.5rem;
    }

    .step-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 2rem 0 3rem 0;
        padding: 2rem;
        background: linear-gradient(135deg, rgba(91, 140, 133, 0.05) 0%, rgba(167, 196, 188, 0.05) 100%);
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(91, 140, 133, 0.1);
        position: relative;
        overflow: hidden;
    }

    .step-indicator::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23a7c4bc' fill-opacity='0.03'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4s-4 1.79-4 4 1.79 4 4 4 4-1.79 4-4zm4-4c2.21 0 4 1.79 4 4s-1.79 4-4 4-4-1.79-4-4 1.79-4 4-4z'/%3E%3C/g%3E%3C/svg%3E") repeat;
        pointer-events: none;
    }

    .step-indicator::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border-radius: 0 0 12px 12px;
        transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 2;
    }

    .step-indicator[data-progress="1"]::after { width: 25%; }
    .step-indicator[data-progress="2"]::after { width: 50%; }
    .step-indicator[data-progress="3"]::after { width: 75%; }
    .step-indicator[data-progress="4"]::after { width: 100%; }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        position: relative;
        z-index: 1;
        max-width: 200px;
        transition: all 0.3s ease;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 8px;
    }

    .step:hover:not(.completed):not(.active) {
        background-color: rgba(91, 140, 133, 0.05);
        transform: translateY(-2px);
    }

    .step:hover:not(.completed):not(.active) .step-number {
        transform: scale(1.05);
        box-shadow: 0 3px 12px rgba(91, 140, 133, 0.2);
    }

    .step-number {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
        color: #6c757d;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 700;
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
        border: 3px solid #e9ecef;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .step-number::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: -1;
    }

    .step.active .step-number {
        background: linear-gradient(135deg, var(--primary-color) 0%, #4a7a74 100%);
        color: white;
        border-color: var(--primary-color);
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(91, 140, 133, 0.3);
    }

    .step.active .step-number::before {
        opacity: 1;
    }

    .step.completed .step-number {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-color: #28a745;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .step-number-text {
        transition: all 0.3s ease;
    }

    .step.completed .step-number-text {
        opacity: 0;
        transform: scale(0);
    }

    .step.completed .step-number::after {
        content: '✓';
        position: absolute;
        font-size: 1.2rem;
        font-weight: bold;
        opacity: 1;
        transform: scale(1);
        transition: all 0.3s ease 0.1s;
    }

    .step-title {
        font-size: 0.95rem;
        color: #6c757d;
        text-align: center;
        font-weight: 500;
        transition: all 0.3s ease;
        line-height: 1.3;
        font-family: 'Open Sans', sans-serif;
    }

    .step.active .step-title {
        color: var(--primary-color);
        font-weight: 700;
        transform: translateY(-2px);
    }

    .step.completed .step-title {
        color: #28a745;
        font-weight: 600;
    }

    .step-connector {
        position: absolute;
        top: 25px;
        left: 50%;
        width: calc(100% - 50px);
        height: 3px;
        background: linear-gradient(90deg, #e9ecef 0%, #dee2e6 100%);
        z-index: 0;
        border-radius: 2px;
        transition: all 0.4s ease;
    }

    .step.completed .step-connector {
        background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
        box-shadow: 0 1px 3px rgba(40, 167, 69, 0.3);
    }

    .step:last-child .step-connector {
        display: none;
    }

    /* Responsive adjustments for step indicator */
    @media (max-width: 768px) {
        .step-indicator {
            padding: 1.5rem 1rem;
            margin: 1.5rem 0 2rem 0;
        }

        .step-number {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .step-title {
            font-size: 0.85rem;
        }

        .step-connector {
            top: 20px;
        }
    }

    @media (max-width: 576px) {
        .step-indicator {
            padding: 1rem 0.5rem;
        }

        .step-number {
            width: 35px;
            height: 35px;
            font-size: 0.9rem;
        }

        .step-title {
            font-size: 0.8rem;
        }

        .step-connector {
            top: 17px;
            height: 2px;
        }
    }

    .loading-spinner {
        display: inline-block;
        width: 1.5rem;
        height: 1.5rem;
        border: 3px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top-color: #4e73df;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .upload-preview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 10px;
        margin-top: 1rem;
    }

    .upload-preview-item {
        position: relative;
        width: 100%;
        padding-bottom: 100%;
        border-radius: 5px;
        overflow: hidden;
        cursor: pointer;
    }

    .upload-preview-item img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .upload-preview-item.selected {
        border: 3px solid #4e73df;
    }

    .upload-preview-item .video-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1 class="mb-4">Create Your Memorial Video</h1>

    <div class="step-indicator" data-progress="2">
        <div class="step completed" id="step1">
            <div class="step-number">
                <span class="step-number-text">1</span>
            </div>
            <div class="step-title">Choose Template</div>
            <div class="step-connector"></div>
        </div>
        <div class="step active" id="step2">
            <div class="step-number">
                <span class="step-number-text">2</span>
            </div>
            <div class="step-title">Select Media</div>
            <div class="step-connector"></div>
        </div>
        <div class="step" id="step3">
            <div class="step-number">
                <span class="step-number-text">3</span>
            </div>
            <div class="step-title">Customize</div>
            <div class="step-connector"></div>
        </div>
        <div class="step" id="step4">
            <div class="step-number">
                <span class="step-number-text">4</span>
            </div>
            <div class="step-title">Payment</div>
        </div>
    </div>

    <!-- Selected Template -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Selected Template: {{ template.name }}</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <img src="{{ url_for('static', filename='images/' + template.preview_image) }}" class="img-fluid rounded" alt="{{ template.name }}">
                </div>
                <div class="col-md-8">
                    <p>{{ template.description }}</p>
                    <a href="{{ url_for('video.templates') }}" class="btn btn-outline-primary">Change Template</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 2: Media Selection -->
    <div id="mediaSelection" class="mb-5">
        <h2 class="mb-3">Step 2: Select Media</h2>
        <p>Choose photos and videos to include in your memorial video.</p>

        <div id="uploadsContainer" class="mb-4">
            <div id="uploadsLoading" class="text-center">
                <div class="loading-spinner"></div>
                <p class="mt-2">Loading your uploads...</p>
            </div>
            <div id="noUploadsMessage" class="alert alert-info d-none">
                You haven't uploaded any photos or videos yet. <a href="{{ url_for('upload.upload_page') }}">Upload now</a>.
            </div>
            <div id="uploadsGrid" class="upload-preview-grid"></div>
        </div>

        <div class="text-center mt-4">
            <button id="nextToCustomizeBtn" class="btn btn-primary btn-lg" disabled>Next: Customize</button>
        </div>
    </div>

    <!-- Step 3: Customization -->
    <div id="customization" class="mb-5" style="display: none;">
        <h2 class="mb-3">Step 3: Customize Your Video</h2>

        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Preview</h5>
                    </div>
                    <div class="card-body">
                        <div class="preview-container">
                            <div id="previewPlaceholder" class="preview-placeholder">
                                <i class="fas fa-film fa-3x mb-3"></i>
                                <p>Click "Generate Preview" to see how your video will look</p>
                            </div>
                            <div id="previewLoading" class="preview-placeholder" style="display: none;">
                                <div class="loading-spinner mb-3"></div>
                                <p>Generating preview...</p>
                            </div>
                            <video id="previewVideo" class="preview-video" controls style="display: none;"></video>
                        </div>
                        <div class="text-center mt-3">
                            <button id="generatePreviewBtn" class="btn btn-primary">Generate Preview</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Customization Options</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group customization-option">
                            <label for="videoTitle">Video Title</label>
                            <input type="text" class="form-control" id="videoTitle" placeholder="Enter a title for your memorial video">
                        </div>

                        <div class="form-group customization-option">
                            <label for="videoDescription">Description</label>
                            <textarea class="form-control" id="videoDescription" rows="3" placeholder="Add a description or message"></textarea>
                        </div>

                        <div class="form-group customization-option">
                            <label for="petName">Pet's Name</label>
                            <input type="text" class="form-control" id="petName" placeholder="Enter your pet's name">
                        </div>

                        <div class="form-group customization-option">
                            <label for="musicStyle">Music Style</label>
                            <select class="form-control" id="musicStyle">
                                <option value="gentle">Gentle & Soothing</option>
                                <option value="uplifting">Uplifting & Joyful</option>
                                <option value="emotional">Emotional & Touching</option>
                                <option value="classical">Classical & Elegant</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <button id="backToMediaBtn" class="btn btn-secondary btn-lg mr-2">Back</button>
            <button id="nextToPaymentBtn" class="btn btn-primary btn-lg">Next: Payment</button>
        </div>
    </div>

    <!-- Step 4: Payment -->
    <div id="payment" class="mb-5" style="display: none;">
        <h2 class="mb-3">Step 4: Complete Your Order</h2>

        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Order Summary</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Selected Template:</h6>
                        <p id="summaryTemplate">{{ template.name }}</p>

                        <h6>Video Title:</h6>
                        <p id="summaryTitle"></p>

                        <h6>Pet's Name:</h6>
                        <p id="summaryPetName"></p>
                    </div>
                    <div class="col-md-6">
                        <h6>Selected Media:</h6>
                        <p id="summaryMediaCount"></p>

                        <h6>Music Style:</h6>
                        <p id="summaryMusicStyle"></p>

                        <h6>Total Price:</h6>
                        <p><strong>${{ price / 100 }}.99</strong></p>
                    </div>
                </div>
            </div>
        </div>

        <form id="paymentForm" action="{{ url_for('video.create_video') }}" method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            <input type="hidden" id="templateIdInput" name="template_id" value="{{ template.id }}">
            <input type="hidden" id="uploadIdsInput" name="upload_ids">
            <input type="hidden" id="titleInput" name="title">
            <input type="hidden" id="descriptionInput" name="description">
            <input type="hidden" id="customizationInput" name="customization">

            <div class="text-center">
                <button id="backToCustomizeBtn" class="btn btn-secondary btn-lg mr-2">Back</button>
                <button type="submit" class="btn btn-success btn-lg">Complete Payment</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Variables to store selections
        let selectedUploadIds = [];
        let previewId = null;
        let previewCheckInterval = null;

        // Step navigation
        const steps = {
            mediaSelection: document.getElementById('mediaSelection'),
            customization: document.getElementById('customization'),
            payment: document.getElementById('payment')
        };

        const stepIndicators = {
            step1: document.getElementById('step1'),
            step2: document.getElementById('step2'),
            step3: document.getElementById('step3'),
            step4: document.getElementById('step4')
        };

        // Media selection
        const uploadsContainer = document.getElementById('uploadsContainer');
        const uploadsLoading = document.getElementById('uploadsLoading');
        const noUploadsMessage = document.getElementById('noUploadsMessage');
        const uploadsGrid = document.getElementById('uploadsGrid');
        const nextToCustomizeBtn = document.getElementById('nextToCustomizeBtn');

        // Load user uploads
        loadUserUploads();

        // Customization
        const generatePreviewBtn = document.getElementById('generatePreviewBtn');
        const previewPlaceholder = document.getElementById('previewPlaceholder');
        const previewLoading = document.getElementById('previewLoading');
        const previewVideo = document.getElementById('previewVideo');

        // Navigation buttons
        nextToCustomizeBtn.addEventListener('click', function() {
            showStep('customization');
            stepIndicators.step2.classList.remove('active');
            stepIndicators.step2.classList.add('completed');
            stepIndicators.step3.classList.add('active');
            updateProgressIndicator(3);
        });

        document.getElementById('backToMediaBtn').addEventListener('click', function() {
            showStep('mediaSelection');
            stepIndicators.step3.classList.remove('active');
            stepIndicators.step2.classList.remove('completed');
            stepIndicators.step2.classList.add('active');
            updateProgressIndicator(2);
        });

        document.getElementById('nextToPaymentBtn').addEventListener('click', function() {
            // Update summary
            document.getElementById('summaryTitle').textContent = document.getElementById('videoTitle').value || 'Not specified';
            document.getElementById('summaryPetName').textContent = document.getElementById('petName').value || 'Not specified';
            document.getElementById('summaryMediaCount').textContent = `${selectedUploadIds.length} items selected`;

            const musicStyleSelect = document.getElementById('musicStyle');
            const musicStyleText = musicStyleSelect.options[musicStyleSelect.selectedIndex].text;
            document.getElementById('summaryMusicStyle').textContent = musicStyleText;

            // Update form inputs
            document.getElementById('uploadIdsInput').value = JSON.stringify(selectedUploadIds);
            document.getElementById('titleInput').value = document.getElementById('videoTitle').value;
            document.getElementById('descriptionInput').value = document.getElementById('videoDescription').value;

            // Create customization object
            const customization = {
                pet_name: document.getElementById('petName').value,
                music_style: document.getElementById('musicStyle').value
            };
            document.getElementById('customizationInput').value = JSON.stringify(customization);

            showStep('payment');
            stepIndicators.step3.classList.remove('active');
            stepIndicators.step3.classList.add('completed');
            stepIndicators.step4.classList.add('active');
            updateProgressIndicator(4);
        });

        document.getElementById('backToCustomizeBtn').addEventListener('click', function() {
            showStep('customization');
            stepIndicators.step4.classList.remove('active');
            stepIndicators.step3.classList.remove('completed');
            stepIndicators.step3.classList.add('active');
            updateProgressIndicator(3);
        });

        // Generate preview
        generatePreviewBtn.addEventListener('click', function() {
            if (selectedUploadIds.length === 0) {
                alert('Please select at least one photo or video in the previous step.');
                return;
            }

            // Show loading state
            previewPlaceholder.style.display = 'none';
            previewLoading.style.display = 'flex';
            previewVideo.style.display = 'none';
            generatePreviewBtn.disabled = true;

            // Create customization object
            const customization = {
                pet_name: document.getElementById('petName').value,
                music_style: document.getElementById('musicStyle').value
            };

            // Call API to generate preview
            fetch('/api/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                },
                body: JSON.stringify({
                    template_id: {{ template.id }},
                    upload_ids: selectedUploadIds,
                    customization: customization
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Store preview ID
                    previewId = data.preview_id;

                    // Start checking for preview status
                    checkPreviewStatus();
                } else {
                    // Show error
                    alert('Error generating preview: ' + (data.error || 'Unknown error'));
                    previewPlaceholder.style.display = 'flex';
                    previewLoading.style.display = 'none';
                    generatePreviewBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error generating preview:', error);
                alert('Error generating preview. Please try again.');
                previewPlaceholder.style.display = 'flex';
                previewLoading.style.display = 'none';
                generatePreviewBtn.disabled = false;
            });
        });

        // Function to check preview status
        function checkPreviewStatus() {
            if (!previewId) return;

            // Clear any existing interval
            if (previewCheckInterval) {
                clearInterval(previewCheckInterval);
            }

            // Set up interval to check status
            previewCheckInterval = setInterval(() => {
                fetch(`/api/preview/${previewId}`, {
                    headers: {
                        'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                    }
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'completed') {
                            // Preview is ready
                            clearInterval(previewCheckInterval);

                            // Show preview video
                            previewVideo.src = data.video_url;
                            previewVideo.style.display = 'block';
                            previewLoading.style.display = 'none';
                            generatePreviewBtn.disabled = false;

                            // Load the video
                            previewVideo.load();
                            previewVideo.play();
                        } else if (data.status === 'failed') {
                            // Preview generation failed
                            clearInterval(previewCheckInterval);
                            alert('Preview generation failed: ' + (data.error || 'Unknown error'));
                            previewPlaceholder.style.display = 'flex';
                            previewLoading.style.display = 'none';
                            generatePreviewBtn.disabled = false;
                        }
                        // If status is still 'processing', continue checking
                    })
                    .catch(error => {
                        console.error('Error checking preview status:', error);
                        clearInterval(previewCheckInterval);
                        alert('Error checking preview status. Please try again.');
                        previewPlaceholder.style.display = 'flex';
                        previewLoading.style.display = 'none';
                        generatePreviewBtn.disabled = false;
                    });
            }, 5000); // Check every 5 seconds
        }

        // Function to load user uploads
        function loadUserUploads() {
            fetch('/api/uploads', {
                headers: {
                    'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                }
            })
                .then(response => response.json())
                .then(data => {
                    uploadsLoading.style.display = 'none';

                    if (data.length === 0) {
                        noUploadsMessage.classList.remove('d-none');
                        return;
                    }

                    // Clear the grid
                    uploadsGrid.innerHTML = '';

                    // Add uploads to the grid
                    data.forEach(upload => {
                        const item = document.createElement('div');
                        item.className = 'upload-preview-item';
                        item.dataset.uploadId = upload.id;

                        if (upload.file_type.startsWith('image/')) {
                            const img = document.createElement('img');
                            img.src = upload.url;
                            img.alt = upload.file_name;
                            item.appendChild(img);
                        } else if (upload.file_type === 'video') {
                            // Video with thumbnail or placeholder
                            if (upload.thumbnail_url) {
                                const img = document.createElement('img');
                                img.src = upload.thumbnail_url;
                                img.alt = upload.file_name;
                                item.appendChild(img);

                                // Add video play icon overlay
                                const playIcon = document.createElement('div');
                                playIcon.className = 'video-play-overlay';
                                playIcon.innerHTML = '<i class="fas fa-play"></i>';
                                playIcon.style.cssText = 'position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.7); color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-size: 12px;';
                                item.appendChild(playIcon);
                            } else {
                                item.style.backgroundColor = '#343a40';

                                const videoIndicator = document.createElement('div');
                                videoIndicator.className = 'video-indicator';
                                videoIndicator.innerHTML = '<i class="fas fa-film"></i>';
                                item.appendChild(videoIndicator);
                            }
                        }

                        // Add click handler
                        item.addEventListener('click', function() {
                            this.classList.toggle('selected');

                            const uploadId = parseInt(this.dataset.uploadId);

                            if (this.classList.contains('selected')) {
                                // Add to selected uploads
                                if (!selectedUploadIds.includes(uploadId)) {
                                    selectedUploadIds.push(uploadId);
                                }
                            } else {
                                // Remove from selected uploads
                                const index = selectedUploadIds.indexOf(uploadId);
                                if (index !== -1) {
                                    selectedUploadIds.splice(index, 1);
                                }
                            }

                            // Enable/disable next button
                            nextToCustomizeBtn.disabled = selectedUploadIds.length === 0;
                        });

                        uploadsGrid.appendChild(item);
                    });
                })
                .catch(error => {
                    console.error('Error loading uploads:', error);
                    uploadsLoading.style.display = 'none';

                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'alert alert-danger';
                    errorMessage.textContent = 'There was an error loading your uploads. Please try refreshing the page.';

                    uploadsContainer.appendChild(errorMessage);
                });
        }

        // Function to show a step and hide others
        function showStep(stepId) {
            Object.keys(steps).forEach(key => {
                steps[key].style.display = key === stepId ? 'block' : 'none';
            });
        }

        // Function to update progress indicator
        function updateProgressIndicator(currentStep) {
            const progressIndicator = document.querySelector('.step-indicator');
            progressIndicator.setAttribute('data-progress', currentStep);
        }
    });
</script>
{% endblock %}
