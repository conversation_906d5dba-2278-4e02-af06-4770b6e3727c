{% extends "base.html" %}

{% block title %}Create Your Memorial Video - FurEverMemories{% endblock %}

{% block styles %}
<style>
    .template-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        cursor: pointer;
    }
    
    .template-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .template-card.selected {
        border: 3px solid #4e73df;
        box-shadow: 0 5px 15px rgba(78, 115, 223, 0.3);
    }
    
    .preview-container {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        background-color: #f8f9fa;
        border-radius: 5px;
        overflow: hidden;
    }
    
    .preview-placeholder {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #6c757d;
    }
    
    .preview-video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .customization-option {
        margin-bottom: 1.5rem;
    }
    
    .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 2rem;
    }
    
    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25%;
        position: relative;
    }
    
    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .step.active .step-number {
        background-color: #4e73df;
        color: white;
    }
    
    .step.completed .step-number {
        background-color: #1cc88a;
        color: white;
    }
    
    .step-title {
        font-size: 0.9rem;
        color: #6c757d;
        text-align: center;
    }
    
    .step.active .step-title {
        color: #4e73df;
        font-weight: bold;
    }
    
    .step.completed .step-title {
        color: #1cc88a;
    }
    
    .step-connector {
        position: absolute;
        top: 20px;
        right: -50%;
        width: 100%;
        height: 2px;
        background-color: #e9ecef;
        z-index: -1;
    }
    
    .step.completed .step-connector {
        background-color: #1cc88a;
    }
    
    .step:last-child .step-connector {
        display: none;
    }
    
    .loading-spinner {
        display: inline-block;
        width: 1.5rem;
        height: 1.5rem;
        border: 3px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top-color: #4e73df;
        animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    .upload-preview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 10px;
        margin-top: 1rem;
    }
    
    .upload-preview-item {
        position: relative;
        width: 100%;
        padding-bottom: 100%;
        border-radius: 5px;
        overflow: hidden;
        cursor: pointer;
    }
    
    .upload-preview-item img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .upload-preview-item.selected {
        border: 3px solid #4e73df;
    }
    
    .upload-preview-item .video-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1 class="mb-4">Create Your Memorial Video</h1>
    

    <!-- Step 1: Template Selection -->
    <div id="templateSelection" class="mb-5">
        <h2 class="mb-3">Step 1: Choose a Template</h2>
        <div class="row">
            {% for template in templates %}
            <div class="col-md-4 mb-4">
                <div class="card h-100 template-card" data-template-id="{{ template.id }}">
                    <img src="{{ url_for('static', filename='images/' + template.preview_image) }}" class="card-img-top" alt="{{ template.name }}">
                    <div class="card-body">
                        <h5 class="card-title">{{ template.name }}</h5>
                        <p class="card-text">{{ template.description }}</p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="text-center mt-4">
            <button id="nextToMediaBtn" class="btn btn-primary btn-lg" disabled>Next: Select Media</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Variables to store selections
        let selectedTemplateId = null;
        let selectedTemplate = null;
        
        // Step navigation
        const steps = {
            templateSelection: document.getElementById('templateSelection')
        };
        
        const stepIndicators = {
            step1: document.getElementById('step1'),
            step2: document.getElementById('step2'),
            step3: document.getElementById('step3'),
            step4: document.getElementById('step4')
        };
        
        // Template selection
        const templateCards = document.querySelectorAll('.template-card');
        const nextToMediaBtn = document.getElementById('nextToMediaBtn');
        
        templateCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove selected class from all cards
                templateCards.forEach(c => c.classList.remove('selected'));
                
                // Add selected class to clicked card
                this.classList.add('selected');
                
                // Store selected template ID
                selectedTemplateId = this.dataset.templateId;
                selectedTemplate = {
                    id: selectedTemplateId,
                    name: this.querySelector('.card-title').textContent,
                    description: this.querySelector('.card-text').textContent
                };
                
                // Enable next button
                nextToMediaBtn.disabled = false;
            });
        });
        
        // Navigation buttons
        nextToMediaBtn.addEventListener('click', function() {
            // Redirect to the create page with the selected template
            window.location.href = `/video/create?template_id=${selectedTemplateId}`;
        });
    });
</script>
{% endblock %}
